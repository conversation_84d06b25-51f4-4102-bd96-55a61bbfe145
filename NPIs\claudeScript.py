import json
from pathlib import Path
from typing import Dict, List
import pandas as pd


class NPPESProcessor:
    def __init__(self, data_file: str, practice_location_file: str, output_file: str):
        self.data_file = data_file
        self.practice_location_file = practice_location_file
        self.output_file = output_file

        self.target_taxonomies = {
            "125K00000X",
            "126800000X",
            "124Q00000X",
            "126900000X",
            "125J00000X",
            "122300000X",
            "1223D0004X",
            "1223D0001X",
            "1223E0200X",
            "1223G0001X",
            "1223P0106X",
            "1223X0008X",
            "1223S0112X",
            "125Q00000X",
            "1223X2210X",
            "1223X0400X",
            "1223P0221X",
            "1223P0300X",
            "1223P0700X",
            "122400000X",
        }

        self.essential_columns = [
            "NPI",
            "Provider Last Name (Legal Name)",
            "Provider First Name",
            "Provider Middle Name",
            "Provider Name Prefix Text",
            "Provider Name Suffix Text",
            "Provider Credential Text",
            "Provider First Line Business Mailing Address",
            "Provider Second Line Business Mailing Address",
            "Provider Business Mailing Address City Name",
            "Provider Business Mailing Address State Name",
            "Provider Business Mailing Address Postal Code",
            "Provider Business Mailing Address Telephone Number",
            "Provider Business Mailing Address Fax Number",
            "Provider First Line Business Practice Location Address",
            "Provider Second Line Business Practice Location Address",
            "Provider Business Practice Location Address City Name",
            "Provider Business Practice Location Address State Name",
            "Provider Business Practice Location Address Postal Code",
            "Provider Business Practice Location Address Telephone Number",
            "Provider Business Practice Location Address Fax Number",
            "Provider Enumeration Date",
            "Last Update Date",
            "Provider Sex Code",
        ]

        self.taxonomy_columns = [
            f"Healthcare Provider Taxonomy Code_{i}" for i in range(1, 16)
        ]
        self.license_columns = [f"Provider License Number_{i}" for i in range(1, 16)]
        self.license_state_columns = [
            f"Provider License Number State Code_{i}" for i in range(1, 16)
        ]
        self.primary_taxonomy_columns = [
            f"Healthcare Provider Primary Taxonomy Switch_{i}" for i in range(1, 16)
        ]

    def has_target_taxonomy(self, row: pd.Series) -> bool:
        for col in self.taxonomy_columns:
            if pd.notna(row[col]) and row[col] in self.target_taxonomies:
                return True
        return False

    def extract_taxonomies(self, row: pd.Series) -> List[Dict]:
        taxonomies = []
        for i in range(1, 16):
            tax_code = row[f"Healthcare Provider Taxonomy Code_{i}"]
            if pd.notna(tax_code) and tax_code in self.target_taxonomies:
                taxonomies.append(
                    {
                        "code": tax_code,
                        "license_number": (
                            row[f"Provider License Number_{i}"]
                            if pd.notna(row[f"Provider License Number_{i}"])
                            else None
                        ),
                        "license_state": (
                            row[f"Provider License Number State Code_{i}"]
                            if pd.notna(row[f"Provider License Number State Code_{i}"])
                            else None
                        ),
                        "is_primary": row[
                            f"Healthcare Provider Primary Taxonomy Switch_{i}"
                        ]
                        == "Y",
                    }
                )
        return taxonomies

    def clean_value(self, value) -> str:
        if pd.isna(value) or value == "":
            return None
        return str(value).strip()

    def process_main_data(self) -> Dict[str, Dict]:
        required_cols = (
            self.essential_columns
            + self.taxonomy_columns
            + self.license_columns
            + self.license_state_columns
            + self.primary_taxonomy_columns
        )
        required_cols.append("Entity Type Code")

        chunk_size = 50000
        providers = {}

        for chunk in pd.read_csv(
            self.data_file,
            chunksize=chunk_size,
            usecols=required_cols,
            dtype=str,
            low_memory=False,
        ):
            type1_chunk = chunk[chunk["Entity Type Code"] == "1"]

            for _, row in type1_chunk.iterrows():
                if self.has_target_taxonomy(row):
                    npi = row["NPI"]
                    taxonomies = self.extract_taxonomies(row)

                    provider_data = {
                        "npi": npi,
                        "name": {
                            "prefix": self.clean_value(
                                row["Provider Name Prefix Text"]
                            ),
                            "first": self.clean_value(row["Provider First Name"]),
                            "middle": self.clean_value(row["Provider Middle Name"]),
                            "last": self.clean_value(
                                row["Provider Last Name (Legal Name)"]
                            ),
                            "suffix": self.clean_value(
                                row["Provider Name Suffix Text"]
                            ),
                            "credentials": self.clean_value(
                                row["Provider Credential Text"]
                            ),
                        },
                        "mailing_address": {
                            "line1": self.clean_value(
                                row["Provider First Line Business Mailing Address"]
                            ),
                            "line2": self.clean_value(
                                row["Provider Second Line Business Mailing Address"]
                            ),
                            "city": self.clean_value(
                                row["Provider Business Mailing Address City Name"]
                            ),
                            "state": self.clean_value(
                                row["Provider Business Mailing Address State Name"]
                            ),
                            "postal_code": self.clean_value(
                                row["Provider Business Mailing Address Postal Code"]
                            ),
                            "phone": self.clean_value(
                                row[
                                    "Provider Business Mailing Address Telephone Number"
                                ]
                            ),
                            "fax": self.clean_value(
                                row["Provider Business Mailing Address Fax Number"]
                            ),
                        },
                        "primary_practice_location": {
                            "line1": self.clean_value(
                                row[
                                    "Provider First Line Business Practice Location Address"
                                ]
                            ),
                            "line2": self.clean_value(
                                row[
                                    "Provider Second Line Business Practice Location Address"
                                ]
                            ),
                            "city": self.clean_value(
                                row[
                                    "Provider Business Practice Location Address City Name"
                                ]
                            ),
                            "state": self.clean_value(
                                row[
                                    "Provider Business Practice Location Address State Name"
                                ]
                            ),
                            "postal_code": self.clean_value(
                                row[
                                    "Provider Business Practice Location Address Postal Code"
                                ]
                            ),
                            "phone": self.clean_value(
                                row[
                                    "Provider Business Practice Location Address Telephone Number"
                                ]
                            ),
                            "fax": self.clean_value(
                                row[
                                    "Provider Business Practice Location Address Fax Number"
                                ]
                            ),
                        },
                        "taxonomies": taxonomies,
                        "sex": self.clean_value(row["Provider Sex Code"]),
                        "enumeration_date": self.clean_value(
                            row["Provider Enumeration Date"]
                        ),
                        "last_update_date": self.clean_value(row["Last Update Date"]),
                        "additional_practice_locations": [],
                    }

                    providers[npi] = provider_data

        return providers

    def process_practice_locations(self, providers: Dict[str, Dict]) -> Dict[str, Dict]:
        if not Path(self.practice_location_file).exists():
            return providers

        practice_cols = [
            "NPI",
            "Provider Secondary Practice Location Address- Address Line 1",
            "Provider Secondary Practice Location Address- Address Line 2",
            "Provider Secondary Practice Location Address - City Name",
            "Provider Secondary Practice Location Address - State Name",
            "Provider Secondary Practice Location Address - Postal Code",
            "Provider Secondary Practice Location Address - Telephone Number",
            "Provider Practice Location Address - Fax Number",
        ]

        chunk_size = 50000
        npi_set = set(providers.keys())

        for chunk in pd.read_csv(
            self.practice_location_file,
            chunksize=chunk_size,
            usecols=practice_cols,
            dtype=str,
            low_memory=False,
        ):
            relevant_chunk = chunk[chunk["NPI"].isin(npi_set)]

            for _, row in relevant_chunk.iterrows():
                npi = row["NPI"]
                if npi in providers:
                    location = {
                        "line1": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address- Address Line 1"
                            ]
                        ),
                        "line2": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address- Address Line 2"
                            ]
                        ),
                        "city": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address - City Name"
                            ]
                        ),
                        "state": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address - State Name"
                            ]
                        ),
                        "postal_code": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address - Postal Code"
                            ]
                        ),
                        "phone": self.clean_value(
                            row[
                                "Provider Secondary Practice Location Address - Telephone Number"
                            ]
                        ),
                        "fax": self.clean_value(
                            row["Provider Practice Location Address - Fax Number"]
                        ),
                    }
                    providers[npi]["additional_practice_locations"].append(location)

        return providers

    def save_to_json(self, data: Dict) -> None:
        output_data = {
            "total_providers": len(data),
            "extraction_date": pd.Timestamp.now().isoformat(),
            "entity_type": 1,
            "target_taxonomies": list(self.target_taxonomies),
            "providers": list(data.values()),
        }

        with open(self.output_file, "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, separators=(",", ":"))

    def process(self) -> None:
        providers = self.process_main_data()
        providers = self.process_practice_locations(providers)
        self.save_to_json(providers)
        print(f"Processed {len(providers)} Type 1 providers with target taxonomies")


def main():
    processor = NPPESProcessor(
        data_file=r"C:\Users\<USER>\Downloads\NPPES_Data_Dissemination_060225_060825_Weekly_V2\npidata_pfile_20250602-20250608.csv",
        practice_location_file=r"C:\Users\<USER>\Downloads\NPPES_Data_Dissemination_060225_060825_Weekly_V2\pl_pfile_20250602-20250608.csv",
        output_file="dentistry_type1_providers.json",
    )
    processor.process()


if __name__ == "__main__":
    main()
